#!/usr/bin/env node

/**
 * Test to verify the WebGL buffer size error fix
 */

import { readFileSync } from 'fs';
import { fileURLToPath } from 'url';
import { dirname, join } from 'path';

const __filename = fileURLToPath(import.meta.url);
const __dirname = dirname(__filename);

// Read the fixed source code
const sourceCode = readFileSync(join(__dirname, 'packages/wave-gradient/src/wave-gradient.js'), 'utf8');

console.log('🔍 Analyzing WebGL buffer size error fixes...\n');

// Check for buffer-specific fixes
const bufferFixes = [
    {
        name: 'Draw parameter validation',
        pattern: /validateDrawParameters/,
        description: 'Validates draw count and buffer size before drawElements'
    },
    {
        name: 'Buffer size tracking',
        pattern: /_elementBufferSize/,
        description: 'Tracks element buffer size for validation'
    },
    {
        name: 'Buffer binding validation',
        pattern: /gl\.bindBuffer\(gl\.ELEMENT_ARRAY_BUFFER, this\._element<PERSON>uffer\)/,
        description: 'Ensures element buffer is properly bound'
    },
    {
        name: 'Geometry validation',
        pattern: /Invalid geometry generated/,
        description: 'Validates geometry before buffer updates'
    },
    {
        name: 'Buffer size calculation',
        pattern: /requiredBufferSize = count \* 4/,
        description: 'Calculates required buffer size for validation'
    },
    {
        name: 'Current buffer check',
        pattern: /gl\.getParameter\(gl\.ELEMENT_ARRAY_BUFFER_BINDING\)/,
        description: 'Checks current element buffer binding'
    },
    {
        name: 'Atomic buffer updates',
        pattern: /clipSpace\.setElements\(geometry\.indices\);\s*this\.drawCount = geometry\.count/,
        description: 'Updates buffer and count atomically'
    },
    {
        name: 'Buffer data validation',
        pattern: /dataBuffer\.byteLength === 0/,
        description: 'Validates buffer data before operations'
    }
];

let allBufferFixesFound = true;

bufferFixes.forEach(fix => {
    const found = fix.pattern.test(sourceCode);
    const status = found ? '✅' : '❌';
    console.log(`${status} ${fix.name}: ${fix.description}`);
    if (!found) {
        allBufferFixesFound = false;
    }
});

console.log('\n📊 Buffer Fix Summary:');
if (allBufferFixesFound) {
    console.log('✅ All WebGL buffer size error fixes have been successfully implemented!');
    console.log('\n🛡️ The buffer fixes include:');
    console.log('   • Draw parameter validation before drawElements calls');
    console.log('   • Element buffer size tracking and validation');
    console.log('   • Proper buffer binding state management');
    console.log('   • Geometry validation during resize operations');
    console.log('   • Buffer size calculation and overflow prevention');
    console.log('   • Current buffer binding verification');
    console.log('   • Atomic buffer and count updates');
    console.log('   • Buffer data validation before operations');
} else {
    console.log('❌ Some buffer fixes are missing. Please review the implementation.');
}

// Analyze the geometry creation function
console.log('\n🔍 Analyzing geometry creation...');
const geometryMatch = sourceCode.match(/static createPlaneGeometry\([\s\S]*?return \{ positions, indices, count: indexCount \};/);
if (geometryMatch) {
    console.log('✅ Geometry creation function found');
    
    // Check for proper index calculation
    const indexCalc = /const indexCount = 3 \* 2 \* gridX \* gridZ;/.test(sourceCode);
    console.log(`${indexCalc ? '✅' : '❌'} Index count calculation: ${indexCalc ? 'Correct' : 'Missing or incorrect'}`);
    
    // Check for proper vertex calculation
    const vertexCalc = /const vertexCount = 3 \* \(gridX \+ 1\) \* \(gridZ \+ 1\);/.test(sourceCode);
    console.log(`${vertexCalc ? '✅' : '❌'} Vertex count calculation: ${vertexCalc ? 'Correct' : 'Missing or incorrect'}`);
} else {
    console.log('❌ Geometry creation function not found');
}

console.log('\n🧪 To test the buffer fixes:');
console.log('   1. Open test-webgl-fix.html in a browser');
console.log('   2. Use "Test Resize" to test geometry regeneration');
console.log('   3. Use "Test Rapid Resize" to stress-test buffer management');
console.log('   4. Monitor console for buffer-related errors');

console.log('\n📝 The buffer size error should now be resolved:');
console.log('   "GL_INVALID_OPERATION: glDrawElements: Insufficient buffer size"');
