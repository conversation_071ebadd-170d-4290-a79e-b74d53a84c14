{"name": "demo", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint", "clean": "rm -rf .turbo && rm -rf node_modules && rm -rf .next"}, "dependencies": {"@headlessui/react": "^1.5.0", "next": "^12.1.7-canary.18", "react": "^18.0.0", "react-dom": "^18.0.0", "wave-gradient": "*"}, "devDependencies": {"autoprefixer": "^10.4.4", "chokidar": "^3.5.3", "eslint-config-next": "^12.1.1", "plaiceholder": "^2.3.0", "prettier-plugin-tailwindcss": "^0.1.8", "tailwindcss": "^3.0.23"}}