{"name": "wave-gradient-pro", "version": "0.1.1", "type": "module", "exports": {".": "./dist/wave-gradient.js"}, "sideEffects": false, "types": "./dist/wave-gradient.d.ts", "files": ["dist"], "repository": {"type": "git", "url": "https://github.com/sa3dany/wave-gradient.git", "directory": "packages/wave-gradient"}, "scripts": {"build": "node scripts/build.js --no-glsl-minify && tsc", "dev": "node scripts/build.js --watch", "lint": "eslint src", "clean": "rm -rf .turbo && rm -rf node_modules && rm -rf dist"}, "devDependencies": {"clang-format": "^1.7.0", "esbuild": "^0.14.39", "eslint-plugin-jsdoc": "^39.2.0"}}