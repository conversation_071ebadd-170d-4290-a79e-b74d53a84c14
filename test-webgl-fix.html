<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>WebGL Fix Test</title>
    <style>
        body {
            margin: 0;
            padding: 20px;
            font-family: Arial, sans-serif;
        }
        canvas {
            width: 100%;
            height: 400px;
            border: 1px solid #ccc;
        }
        .controls {
            margin: 20px 0;
        }
        button {
            margin: 5px;
            padding: 10px 15px;
            background: #007bff;
            color: white;
            border: none;
            border-radius: 4px;
            cursor: pointer;
        }
        button:hover {
            background: #0056b3;
        }
        .log {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            padding: 10px;
            margin-top: 20px;
            height: 200px;
            overflow-y: auto;
            font-family: monospace;
            font-size: 12px;
        }
    </style>
</head>
<body>
    <h1>WebGL Error Fix Test</h1>
    <p>This test verifies that the WebGL uniform error has been fixed.</p>

    <canvas id="gradient-canvas"></canvas>

    <div class="controls">
        <button onclick="createGradient()">Create Gradient</button>
        <button onclick="destroyGradient()">Destroy Gradient</button>
        <button onclick="recreateGradient()">Recreate Gradient</button>
        <button onclick="testResize()">Test Resize</button>
        <button onclick="testRapidResize()">Test Rapid Resize</button>
        <button onclick="simulateContextLoss()">Simulate Context Loss</button>
        <button onclick="clearLog()">Clear Log</button>
    </div>

    <div id="log" class="log"></div>

    <script type="module">
        import { WaveGradient } from './packages/wave-gradient/src/wave-gradient.js';

        let gradient = null;
        const canvas = document.getElementById('gradient-canvas');
        const logElement = document.getElementById('log');

        // Override console methods to capture logs
        const originalConsole = {
            log: console.log,
            warn: console.warn,
            error: console.error
        };

        function logToPage(type, ...args) {
            const timestamp = new Date().toLocaleTimeString();
            const message = args.map(arg =>
                typeof arg === 'object' ? JSON.stringify(arg) : String(arg)
            ).join(' ');

            const logEntry = document.createElement('div');
            logEntry.style.color = type === 'error' ? 'red' : type === 'warn' ? 'orange' : 'black';
            logEntry.textContent = `[${timestamp}] ${type.toUpperCase()}: ${message}`;
            logElement.appendChild(logEntry);
            logElement.scrollTop = logElement.scrollHeight;

            // Also call original console method
            originalConsole[type](...args);
        }

        console.log = (...args) => logToPage('log', ...args);
        console.warn = (...args) => logToPage('warn', ...args);
        console.error = (...args) => logToPage('error', ...args);

        window.createGradient = function() {
            try {
                if (gradient) {
                    console.warn('Gradient already exists, destroying first');
                    gradient.destroy();
                }

                console.log('Creating new gradient...');
                gradient = new WaveGradient(canvas, {
                    colors: ['#ef008f', '#6ec3f4', '#7038ff', '#ffba27'],
                    amplitude: 320,
                    speed: 1.25
                });
                console.log('Gradient created successfully');
            } catch (error) {
                console.error('Failed to create gradient:', error);
            }
        };

        window.destroyGradient = function() {
            if (gradient) {
                console.log('Destroying gradient...');
                gradient.destroy();
                gradient = null;
                console.log('Gradient destroyed');
            } else {
                console.warn('No gradient to destroy');
            }
        };

        window.recreateGradient = function() {
            console.log('Recreating gradient...');
            destroyGradient();
            setTimeout(() => createGradient(), 100);
        };

        window.simulateContextLoss = function() {
            console.log('Simulating WebGL context loss...');
            const gl = canvas.getContext('webgl2');
            if (gl && gl.getExtension('WEBGL_lose_context')) {
                gl.getExtension('WEBGL_lose_context').loseContext();
                console.log('Context loss simulated');
            } else {
                console.warn('Cannot simulate context loss - extension not available');
            }
        };

        window.testResize = function() {
            console.log('Testing resize functionality...');
            if (!gradient) {
                console.warn('No gradient to resize');
                return;
            }

            // Simulate different canvas sizes
            const sizes = [
                [400, 300],
                [800, 600],
                [1200, 800],
                [600, 400]
            ];

            let index = 0;
            const resizeInterval = setInterval(() => {
                if (index >= sizes.length) {
                    clearInterval(resizeInterval);
                    console.log('Resize test completed');
                    return;
                }

                const [width, height] = sizes[index];
                console.log(`Resizing canvas to ${width}x${height}`);
                canvas.style.width = width + 'px';
                canvas.style.height = height + 'px';

                // Trigger resize by dispatching a resize event
                window.dispatchEvent(new Event('resize'));

                index++;
            }, 1000);
        };

        window.testRapidResize = function() {
            console.log('Testing rapid resize (stress test)...');
            if (!gradient) {
                console.warn('No gradient to resize');
                return;
            }

            let count = 0;
            const maxCount = 20;
            const rapidResizeInterval = setInterval(() => {
                if (count >= maxCount) {
                    clearInterval(rapidResizeInterval);
                    console.log('Rapid resize test completed');
                    return;
                }

                const width = 400 + Math.random() * 800;
                const height = 300 + Math.random() * 500;
                console.log(`Rapid resize ${count + 1}/${maxCount}: ${Math.round(width)}x${Math.round(height)}`);

                canvas.style.width = width + 'px';
                canvas.style.height = height + 'px';
                window.dispatchEvent(new Event('resize'));

                count++;
            }, 100);
        };

        window.clearLog = function() {
            logElement.innerHTML = '';
        };

        // Create initial gradient
        createGradient();

        console.log('Test page loaded. Try the buttons above to test the WebGL fixes.');
        console.log('New tests available:');
        console.log('• Test Resize: Tests geometry regeneration during resize');
        console.log('• Test Rapid Resize: Stress tests buffer management');
    </script>
</body>
</html>
